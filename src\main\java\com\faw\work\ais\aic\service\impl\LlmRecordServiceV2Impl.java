package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.enums.LLMBizTypeEnum;
import com.faw.work.ais.aic.common.enums.MessageTypeEnum;
import com.faw.work.ais.aic.common.mq.MessageProducer;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.common.util.DateUtils;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.aic.common.util.StrUtils;
import com.faw.work.ais.aic.config.BaiLianAppConfig;
import com.faw.work.ais.aic.feign.DgwOpenApiFeignClient;
import com.faw.work.ais.aic.feign.dto.DgwResult;
import com.faw.work.ais.aic.mapper.llm.LlmRecordMapper;
import com.faw.work.ais.aic.model.domain.LlmRecord;
import com.faw.work.ais.aic.model.dto.*;
import com.faw.work.ais.aic.model.request.AiRequest;
import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.response.AiResponse;
import com.faw.work.ais.aic.model.response.DmsEmotionResponse;
import com.faw.work.ais.aic.service.AudioProcessingService;
import com.faw.work.ais.aic.service.LlmRecordService;
import com.faw.work.ais.aic.service.LlmRecordServiceV2;
import com.faw.work.ais.aic.service.OmniService;
import com.faw.work.ais.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 情绪分析表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LlmRecordServiceV2Impl extends ServiceImpl<LlmRecordMapper, LlmRecord> implements LlmRecordServiceV2 {

    private static final String STATUS_UNPROCESSED = "00";
    private static final String STATUS_PROCESSING = "01";
    private static final String STATUS_COMPLETED = "02";
    private static final String STATUS_FAILED = "10";
    @Autowired
    private DgwOpenApiFeignClient dgwFeignClient;
    @Autowired
    private ChatClient chatClient;
    @Autowired
    private LlmRecordMapper llmRecordMapper;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private BaiLianAppConfig baiLianAppConfig;

    @Autowired
    private LlmRecordService self;
    @Autowired
    private Executor dmsEmotionExecutor;

    @Autowired
    private RedisService redisService;

    @Autowired
    private OmniService omniService;

    @Autowired
    private AudioProcessingService audioProcessingService;

    private TopicSummaryDTO performTopicSummary(String requestId) {
        List<LlmRecord> all = llmRecordMapper.selectByRequestId(requestId, null, LLMBizTypeEnum.DMS_EMOTION_V2.getCode());
        // 全文内容
        StringBuilder allContext = new StringBuilder();
        for (LlmRecord slice : all) {
            allContext.append(slice.getUserInput());
        }
        TopicSummaryDTO summaryDTO = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionTopicAppId(),
                allContext.toString(),
                TopicSummaryDTO.class
        );
        if (summaryDTO != null){
            summaryDTO.setInput(allContext.toString());
        }
        log.info("话题总结结果: {}", JSONUtil.toJsonStr(summaryDTO));
        return summaryDTO;
    }

    private void performTagExtraction(LlmRecord emotion) {
        log.info("开始对记录ID: {} 进行标签提取", emotion.getId());
        TagAnalysisDTO tagResult = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionTagAppId(),
                emotion.getUserInput(),
                TagAnalysisDTO.class
        );
        // LLM
        if (tagResult != null) {
            // String tagContent = formatTagContent(tagResult);
            LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LlmRecord::getId, emotion.getId())
                    .set(LlmRecord::getLlmOutput, JSONUtil.toJsonStr(tagResult))
                    .set(LlmRecord::getField3, tagResult.getStart())
                    .set(LlmRecord::getField4, tagResult.getEnd())
            ;
            this.update(updateWrapper);
            log.info("记录ID: {} 标签提取成功", emotion.getId());
        }
    }

    @Override
    public AiResponse testChat(AiRequest request) {

        // 1. 构建ChatClient请求，并设置用户输入
        ChatClient.ChatClientRequestSpec chatRequest = chatClient.prompt()
                .user(request.getUserInput());


        // 2. 如果有自定义prompt设置，则作为系统消息使用
        if (StrUtil.isNotBlank(request.getPrompt())) {
            chatRequest = chatRequest.system(request.getPrompt());
        }

        // 3. 设置模型、温度、最大长度等参数
        // 使用.options()来配置模型参数
        ChatOptions chatOptions = DashScopeChatOptions.builder().withModel(request.getModelName())
                .withTemperature(request.getTemperature())
                .withMaxToken(request.getMaxResponseLength()).build();
        chatRequest.options(chatOptions);

        // 4. 调用AI模型并获取非流式响应
        ChatResponse chatResponse = chatRequest.call().chatResponse();

        // 5. 构建成功的响应结果
        return AiResponse.builder()
                .content(chatResponse.getResult().getOutput().getText())
                .build();
    }

    @Override
    public void processConversationAbTest(ProcessRequest request) {

        try {
            String requestId = request.getRequestId();
            List<LlmRecord> llmRecords = llmRecordMapper.selectByRequestId(requestId, null, null);
            if (CollUtil.isNotEmpty(llmRecords)) {
                throw new IllegalArgumentException("请求ID已存在，请勿重复提交");
            }

            String conversationContent= request.getUserInput();

            if (StrUtil.isBlank(conversationContent)) {
                throw new IllegalArgumentException("用户输入不能为空");
            }
            // TODO: 调用大模型根据对话内容分析说话角色

            String customerSpeaker = "客户";
            // 分片客户对话内容
            List<String> slices = StrUtils.sliceConversation(conversationContent, customerSpeaker, 5, 1);
            if (CollUtil.isEmpty(slices)) {
                throw new IllegalArgumentException("对话内容中没有客户的说话内容，分片为空");
            }

            List<LlmRecord> emotionList = new ArrayList<>();
            String now = DateUtils.getCurrentDateTimeString();
            for (String slice : slices) {
                LlmRecord emotion = new LlmRecord();
                emotion.setRequestId(requestId);
                emotion.setUserInput(slice);
                emotion.setStatus(STATUS_UNPROCESSED);
                emotion.setBizType(LLMBizTypeEnum.DMS_EMOTION.getCode());
                emotion.setCallbackUrl("http://localhost:8080/api/v1/llm/emotion/callback");
                emotion.setField2(request.getAudioUrl());
                emotion.setCreateAt(now);
                emotionList.add(emotion);
            }

            this.saveBatch(emotionList);
            log.info("成功将 {} 个情绪分析对话分片存入数据库", emotionList.size());

            List<String> productSlices = StrUtils.sliceConversation(conversationContent, customerSpeaker, 5, 1);
            List<LlmRecord> productList = new ArrayList<>();
            for (String productSlice : productSlices) {
                LlmRecord emotion = new LlmRecord();
                emotion.setRequestId(requestId);
                emotion.setUserInput(productSlice);
                emotion.setStatus(STATUS_UNPROCESSED);
                emotion.setBizType(LLMBizTypeEnum.DMS_PRODUCT.getCode());
                emotion.setCallbackUrl("http://localhost:8080/api/v1/llm/emotion/callback");
                emotion.setField2(request.getAudioUrl());
                emotion.setCreateAt(now);
                productList.add(emotion);
            }

            this.saveBatch(productList);
            log.info("成功将 {} 个产品需求分析对话分片存入数据库", productList.size());

            // 将提示词存到Redis,TTL为60分钟
            redisService.set("emotion:" + requestId, JSONUtil.toJsonStr(request.getAiRequest()),5*60*12);
            // 发送MQ消息
            messageProducer.sendMessage(requestId, MessageTypeEnum.DMS_EMOTION_PRODUCT_AB.getCode());

        } catch (Exception e) {
            log.error("处理请求失败", e);
            throw new BizException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processSliceAbTest(String requestId) {
        List<LlmRecord> list = llmRecordMapper.selectByRequestId(requestId, STATUS_UNPROCESSED, null);

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (LlmRecord slice : list) {
            // 更新状态为处理中
            log.info("开始处理分片ID: {}", slice.getId());
            self.updateStatusTran(slice.getId(), STATUS_PROCESSING, null);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 串行执行情绪分析和标签提取
                    if (slice.getBizType().equals(LLMBizTypeEnum.DMS_EMOTION.getCode())) {
                        performEmotionAnalysisAbTest(slice);
                    }
                    if (slice.getBizType().equals(LLMBizTypeEnum.DMS_PRODUCT.getCode())) {
                        performTagExtraction(slice);
                    }

                    self.updateStatusTran(slice.getId(), STATUS_COMPLETED, null);
                    log.info("分片ID: {} 处理成功", slice.getId());
                } catch (Exception e) {
                    log.error("分片ID: {} 处理失败", slice.getId(), e);
                    self.updateStatusTran(slice.getId(), STATUS_FAILED, null);
                }
            }, dmsEmotionExecutor);

            futures.add(future);
        }

        // 等待所有异步任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("等待异步任务完成时发生异常", e);
            return;
        }

        // 调用话题总结模型
        TopicSummaryDTO topicSummaryDTO = performTopicSummary(requestId);
        // 组装数据
        DmsEmotionResponse responseDTO = new DmsEmotionResponse();
        // 填充情绪分析结果
        EmotionResponseSummaryDTO emotion = new EmotionResponseSummaryDTO();
        emotion.setEmotionSummary(topicSummaryDTO.getTotalEmotion());

        List<LlmRecord> finishEmotionList = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_EMOTION.getCode());
        List<EmotionResponseDTO> emotionList = new ArrayList<>();
        for (LlmRecord llmRecord : finishEmotionList) {
            EmotionResponseDTO emotionResponseDTO = JSONUtil.toBean(llmRecord.getLlmOutput(), EmotionResponseDTO.class);
            emotionResponseDTO.setInput(llmRecord.getUserInput());
            emotionResponseDTO.setLlmRecordId(llmRecord.getId());
            emotionList.add(emotionResponseDTO);
        }
        emotion.setEmotionList(emotionList);


        responseDTO.setEmotionData(emotion);

        // 填充标签提取结果
        List<LlmRecord> finishTagList = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_PRODUCT.getCode());

        List<TagAnalysisDTO> tagData = new ArrayList<>();
        for (LlmRecord llmRecord : finishTagList) {
            TagAnalysisDTO tag = JSONUtil.toBean(llmRecord.getLlmOutput(), TagAnalysisDTO.class);
            tag.setLlmRecordId(llmRecord.getId());
            tag.setInput(llmRecord.getUserInput());
            List<TagAnalysisDTO.TopicSummary> customerQuestionSummaries = tag.getCustomerQuestionSummaries();
            // 过滤掉question或answer为空的记录
            customerQuestionSummaries.removeIf(summary -> StrUtil.isBlank(summary.getQuestion()) || StrUtil.isBlank(summary.getAnswer()));
            // 过滤掉question或answer过短的记录
            tag.setTopicSummaries(customerQuestionSummaries);
            tagData.add(tag);
        }
        responseDTO.setTagData(tagData);
        responseDTO.setTopicData(topicSummaryDTO);
        responseDTO.setRequestId(requestId);
        // 回调接口
        log.info("开始回调接口, 请求参数为: {}", JSONUtil.toJsonStr(responseDTO));

        DgwResult dgwResult = dgwFeignClient.callBackDmsEmotion(responseDTO);

        log.info("回调接口成功, 接口返回: {}", JSONUtil.toJsonStr(dgwResult));


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processSliceV2(String requestId) {
        log.info("开始处理分片数据V2，请求ID: {}", requestId);

        List<LlmRecord> list = llmRecordMapper.selectByRequestId(requestId, STATUS_UNPROCESSED, LLMBizTypeEnum.DMS_EMOTION_V2.getCode());

        if (CollUtil.isEmpty(list)) {
            log.warn("未找到需要处理的分片, requestId: {}", requestId);
            checkAndFinalizeProcessing(requestId);
            return;
        }

        log.info("找到 {} 个未处理的分片", list.size());

        // 1. 预处理音频文件 - 在开始处理前就下载并切分所有音频,key为LlmRecord的ID，value为音频文件路径
        Map<Long, String> audioFileMap = new HashMap<>(16);
        try {
            // 获取音频URL（假设所有分片使用同一个音频文件）
            String audioUrl = list.get(0).getField2();

            log.info("开始预处理音频文件，URL: {}", audioUrl);
            audioFileMap = audioProcessingService.preprocessAudioFiles(audioUrl, list);
            log.info("音频预处理完成，生成 {} 个音频文件", audioFileMap.size());

        } catch (Exception e) {
            log.error("音频预处理失败，继续处理但语气分析可能受影响", e);
        }

        // 2. 异步处理每个分片
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (LlmRecord slice : list) {
            log.info("开始处理分片ID: {}", slice.getId());
            self.updateStatusTran(slice.getId(), STATUS_PROCESSING, null);

            // 获取对应的音频文件路径
            String audioFilePath = audioFileMap.get(slice.getId());

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 保存大模型输出
                    TagAnalysisDTO llmOutput = new TagAnalysisDTO();
                    llmOutput.setInput(slice.getUserInput());
                    llmOutput.setLlmRecordId(slice.getId());

                    performEmotionAndTopicAnalysis(slice, llmOutput, audioFilePath);
                    performTagAnalysis(slice, llmOutput);

                    LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(LlmRecord::getId, slice.getId())
                            .set(LlmRecord::getStatus, STATUS_COMPLETED)
                            .set(LlmRecord::getLlmOutput, JSONUtil.toJsonStr(llmOutput));
                    this.update(updateWrapper);
                    log.info("记录ID: {} 统一分析成功，并已将转换后格式的数据存入数据库。", slice.getId());

                    // 处理完成后清理对应的音频文件
                    if (StrUtil.isNotBlank(audioFilePath)) {
                        audioProcessingService.cleanupAudioFile(audioFilePath);
                    }

                } catch (Exception e) {
                    log.error("分片ID: {} 处理失败", slice.getId(), e);
                    self.updateStatusTran(slice.getId(), STATUS_FAILED, null);

                    // 处理失败也要清理音频文件
                    if (StrUtil.isNotBlank(audioFilePath)) {
                        audioProcessingService.cleanupAudioFile(audioFilePath);
                    }
                }
            }, dmsEmotionExecutor);
            futures.add(future);
        }

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("等待异步任务完成时发生异常, requestId: {}", requestId, e);
        }

        assembleAndCallback(requestId);
    }

    /**
     * 执行标签分析
     *
     * @param slice  LlmRecord分片
     */
    private void performTagAnalysis(LlmRecord slice, TagAnalysisDTO finalDto) {
        log.info("开始对记录ID: {} 进行标签分析", slice.getId());
        String userInput = slice.getUserInput();

        // 调用标签分析模型
        LlmAnalysisResultDTO.DemandTagsAnalysis tagResult = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionTagDemandAppIdV2(),
                userInput,
                LlmAnalysisResultDTO.DemandTagsAnalysis.class
        );

        if (tagResult == null) {
            log.error("记录ID: {} 标签分析失败，LLM返回结果为空，用户输入:{}", slice.getId(), userInput);
            // 即使LLM返回为空，也要初始化空集合，确保JSON字段存在
            finalDto.setDemandTags(new ArrayList<>());
            finalDto.setAiDemandTags(new ArrayList<>());
            return;
        }

        // 确保即使大模型返回的集合为null，也设置为空集合而不是null
        finalDto.setDemandTags(tagResult.getDemandTags() != null ? tagResult.getDemandTags() : new ArrayList<>());
        finalDto.setAiDemandTags(tagResult.getAiDemandTags() != null ? tagResult.getAiDemandTags() : new ArrayList<>());

    }


    /**
     * 【核心修改点】
     * 调用统一分析模型，接收扁平化结果，然后转换为结构化DTO并存入数据库。
     *
     * @param slice         LlmRecord分片
     * @param finalDto      最终的DTO对象
     * @param audioFilePath 预处理的音频文件路径（可为空）
     */
    private void performEmotionAndTopicAnalysis(LlmRecord slice, TagAnalysisDTO finalDto, String audioFilePath) {
        log.info("开始对记录ID: {} 进行统一分析（情绪+标签）", slice.getId());
        String userInput = slice.getUserInput();
        // 1. 调用LLM，使用临时DTO (LlmAnalysisResultDTO) 接收扁平化的JSON结果

        LlmAnalysisResultDTO.CustomerAnalysis llmResult = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionAndTopicAnalysisAppIdV2(),
                userInput,
                LlmAnalysisResultDTO.CustomerAnalysis.class
        );

        if (llmResult == null){
            log.error("记录ID: {} 统一分析失败，LLM返回结果为空，用户输入:{}", slice.getId(),userInput);
            return;
        }


        finalDto.setLlmRecordId(slice.getId());
            finalDto.setStart(llmResult.getStart());
            finalDto.setEnd(llmResult.getEnd());
        // finalDto.setCustomerQuestionSummaries(llmResult.getCustomerQuestionSummaries());


        // --- 这是关键的转换逻辑 ---
        // 创建嵌套的EmotionAnalysis对象
        TagAnalysisDTO.EmotionAnalysis semanticEmotion = new TagAnalysisDTO.EmotionAnalysis();
        semanticEmotion.setEmotion(llmResult.getEmotion());
        semanticEmotion.setReason(llmResult.getReason());
        semanticEmotion.setScore(llmResult.getScore());
        finalDto.setSemanticEmotion(semanticEmotion);

        // 语气情绪分析 - 使用Omni多模态模型
        TagAnalysisDTO.EmotionAnalysis toneEmotion = performToneAnalysisWithOmni(slice, userInput, audioFilePath);
        finalDto.setToneEmotion(toneEmotion);

        // 行为情绪分析
        TagAnalysisDTO.EmotionAnalysis actionEmotion = new TagAnalysisDTO.EmotionAnalysis();
        actionEmotion.setEmotion(llmResult.getEmotion());
        actionEmotion.setReason(llmResult.getReason());
        actionEmotion.setScore(llmResult.getScore());
        finalDto.setBehaviorEmotion(actionEmotion);



    }

    private boolean checkAndFinalizeProcessing(String requestId) {
        long totalCount = llmRecordMapper.selectCount(new LambdaQueryWrapper<LlmRecord>().eq(LlmRecord::getRequestId, requestId));
        if (totalCount == 0) {
            log.warn("请求ID {} 没有任何分片记录。", requestId);
            return true;
        }
        long completedCount = llmRecordMapper.selectCount(new LambdaQueryWrapper<LlmRecord>().eq(LlmRecord::getRequestId, requestId).eq(LlmRecord::getStatus, STATUS_COMPLETED));
        if (totalCount == completedCount) {
            log.info("请求ID: {} 的所有分片均已处理完成，开始组装回调。", requestId);
            return assembleAndCallback(requestId);
        }
        return true;
    }



    private boolean assembleAndCallback(String requestId) {
        log.info("开始组装数据并执行回调，请求ID: {}", requestId);

        // 3. 执行话题总结，这个是针对所有分片的，如果失败则影响全局
        TopicSummaryDTO topicSummaryDTO = performTopicSummary(requestId);

        // 4. 获取所有已完成的分片记录
        List<LlmRecord> completedSlices = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_EMOTION_V2.getCode());

        DmsEmotionResponse responseDTO = new DmsEmotionResponse();
        List<TagAnalysisDTO> tagDataList = new ArrayList<>();


        for (LlmRecord llmRecord : completedSlices) {
            try {
                TagAnalysisDTO tagAnalysisDTO = JSONUtil.toBean(llmRecord.getLlmOutput(), TagAnalysisDTO.class);

                if (tagAnalysisDTO == null) {
                    log.warn("解析分片ID {} 的llm_output为空或无效，跳过该分片。", llmRecord.getId());
                    continue;
                }

                // 确保llmRecordId已设置（防止数据不一致）
                if (tagAnalysisDTO.getLlmRecordId() == null) {
                    tagAnalysisDTO.setLlmRecordId(llmRecord.getId());
                }

                // 清理无效的问答对
                if (CollUtil.isNotEmpty(tagAnalysisDTO.getCustomerQuestionSummaries())) {
                    tagAnalysisDTO.getCustomerQuestionSummaries().removeIf(summary -> StrUtil.isBlank(summary.getQuestion()) || StrUtil.isBlank(summary.getAnswer()));
                }

                // 确保demandTags和aiDemandTags字段存在（即使为空集合）
                if (tagAnalysisDTO.getDemandTags() == null) {
                    tagAnalysisDTO.setDemandTags(new ArrayList<>());
                }
                if (tagAnalysisDTO.getAiDemandTags() == null) {
                    tagAnalysisDTO.setAiDemandTags(new ArrayList<>());
                }

                // 成功处理完一个分片，将其加入到最终的列表中
                tagDataList.add(tagAnalysisDTO);

            } catch (Exception e) {
                // 如果JSONUtil.toBean解析失败（比如llm_output格式损坏），会在这里捕获
                log.error("处理分片ID {} 时发生异常，已跳过该分片。异常信息: {}", llmRecord.getId(), e.getMessage());
            }
        }

        // 6. 如果经过筛选后，没有任何有效数据可以回调，则提前终止
        if (tagDataList.isEmpty()) {
            log.warn("请求ID: {} 的所有已完成分片均无法解析或数据无效，不执行回调。", requestId);
            return true;
        }

        // 7. 使用成功组装的数据列表进行后续操作
        responseDTO.setRequestId(requestId);
        responseDTO.setEmotionSummary(topicSummaryDTO != null ? topicSummaryDTO.getTotalEmotion() : null);
        responseDTO.setTagData(tagDataList);
        responseDTO.setTopicData(topicSummaryDTO);

        log.info("开始回调接口（共 {} 条有效数据）, 请求参数为: {}", tagDataList.size(), JSONUtil.toJsonStr(responseDTO));
        DgwResult dgwResult = dgwFeignClient.callBackDmsEmotion(responseDTO);
        log.info("回调接口成功, 接口返回: {}", JSONUtil.toJsonStr(dgwResult));

        return true;
    }


    private void performEmotionAnalysisAbTest(LlmRecord emotion) {
        Object value = redisService.get("emotion:" + emotion.getRequestId());

        log.info("开始对记录ID: {} 进行AB情绪分析", emotion.getId());
        AiRequest aiRequest = JSONUtil.toBean(value.toString(), AiRequest.class);
        AiRequest build = AiRequest.builder()
                .modelName(aiRequest.getModelName())
                .userInput(emotion.getUserInput())
                .temperature(aiRequest.getTemperature())
                .prompt(aiRequest.getPrompt())

                .maxResponseLength(aiRequest.getMaxResponseLength())
                .build();
        AiResponse aiResponse = this.testChat(build);
        String content = aiResponse.getContent();
        EmotionResponseDTO result=JSONUtil.toBean(content,EmotionResponseDTO.class);

        if (result != null) {
            // 设置llmRecordId
            result.setLlmRecordId(emotion.getId());

            LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LlmRecord::getId, emotion.getId())
                    .set(LlmRecord::getLlmOutput, JSONUtil.toJsonStr(result))
                    .set(LlmRecord::getField3, result.getStart())
                    .set(LlmRecord::getField4, result.getEnd())
            ;
            this.update(updateWrapper);
            log.info("记录ID: {} 情绪分析成功", emotion.getId());
        }
    }

    /**
     * 使用Omni多模态模型进行语气分析
     *
     * @param slice         LlmRecord分片
     * @param userInput     用户输入文本
     * @param audioFilePath 预处理的音频文件路径（可为空）
     * @return 语气情绪分析结果
     */
    private TagAnalysisDTO.EmotionAnalysis performToneAnalysisWithOmni(LlmRecord slice, String userInput, String audioFilePath) {
        log.info("开始对记录ID: {} 进行Omni语气分析", slice.getId());

        try {
            byte[] audioBytes = null;

            // 1. 优先使用预处理的音频文件
            if (StrUtil.isNotBlank(audioFilePath)) {
                log.info("使用预处理的音频文件: {}", audioFilePath);
                audioBytes = audioProcessingService.readAudioFile(audioFilePath);
            } else {
                // 2. 如果没有预处理文件，则实时处理
                log.warn("没有预处理音频文件，尝试实时处理");

                String audioUrl = slice.getField2();
                if (StrUtil.isBlank(audioUrl)) {
                    log.error("记录ID: {} 没有音频URL，跳过语气分析", slice.getId());
                    return createDefaultEmotionAnalysis(userInput);
                }

                String timestamps = slice.getField3();
                if (StrUtil.isBlank(timestamps)) {
                    log.error("记录ID: {} 没有时间戳信息，跳过语气分析", slice.getId());
                    return createDefaultEmotionAnalysis(userInput);
                }

                // 实时切分并合并音频
                audioBytes = audioProcessingService.splitAndMergeAudio(audioUrl, timestamps);
            }

            if (audioBytes == null || audioBytes.length == 0) {
                log.warn("记录ID: {} 音频数据为空，跳过语气分析", slice.getId());
                return createDefaultEmotionAnalysis(userInput);
            }

            // 3. 构建语气分析提示词
            String prompt = buildToneAnalysisPrompt(userInput);
            String systemMessage = "你是一个专业的语音情感分析专家，请根据音频中的语气、语调、语速等特征分析说话者的情绪状态。";

            // 4. 调用Omni模型进行分析
            String omniResult = omniService.processAudioWithAudioBase64(audioBytes, prompt, systemMessage);

            // 5. 解析Omni结果为情绪分析对象
            return parseOmniResultToEmotionAnalysis(omniResult);

        } catch (Exception e) {
            log.error("记录ID: {} Omni语气分析失败", slice.getId(), e);
            return createDefaultEmotionAnalysis(userInput);
        }
    }

    /**
     * 构建语气分析提示词
     */
    private String buildToneAnalysisPrompt(String userInput) {
        return String.format(
            "请分析这段音频中说话者的语气情绪。对话内容是：\"%s\"。" +
            "请从语气、语调、语速等维度分析说话者的情绪状态，" +
            "并返回JSON格式：{\"emotion\":\"情绪类型\",\"score\":\"情绪强度(0-1)\",\"reason\":\"分析原因\"}。" +
            "情绪类型包括：积极、消极、中性、愤怒、高兴、悲伤、焦虑等。",
            userInput
        );
    }

    /**
     * 解析Omni结果为情绪分析对象
     */
    private TagAnalysisDTO.EmotionAnalysis parseOmniResultToEmotionAnalysis(String omniResult) {
        try {
            // 尝试解析JSON结果
            TagAnalysisDTO.EmotionAnalysis emotionAnalysis = JSONUtil.toBean(omniResult, TagAnalysisDTO.EmotionAnalysis.class);
            if (emotionAnalysis != null && StrUtil.isNotBlank(emotionAnalysis.getEmotion())) {
                return emotionAnalysis;
            }
        } catch (Exception e) {
            log.warn("解析Omni结果失败，使用文本解析: {}", omniResult, e);
        }

        // 如果JSON解析失败，尝试从文本中提取信息
        return parseTextToEmotionAnalysis(omniResult);
    }

    /**
     * 从文本中解析情绪分析结果
     */
    private TagAnalysisDTO.EmotionAnalysis parseTextToEmotionAnalysis(String text) {
        TagAnalysisDTO.EmotionAnalysis emotionAnalysis = new TagAnalysisDTO.EmotionAnalysis();

        if (StrUtil.isBlank(text)) {
            return createDefaultEmotionAnalysis(text);
        }

        // 简单的文本解析逻辑
        String lowerText = text.toLowerCase();

        // 情绪识别
        if (lowerText.contains("积极") || lowerText.contains("高兴") || lowerText.contains("愉快")) {
            emotionAnalysis.setEmotion("积极");
            emotionAnalysis.setScore("0.7");
        } else if (lowerText.contains("消极") || lowerText.contains("悲伤") || lowerText.contains("沮丧")) {
            emotionAnalysis.setEmotion("消极");
            emotionAnalysis.setScore("0.6");
        } else if (lowerText.contains("愤怒") || lowerText.contains("生气") || lowerText.contains("愤慨")) {
            emotionAnalysis.setEmotion("愤怒");
            emotionAnalysis.setScore("0.8");
        } else if (lowerText.contains("焦虑") || lowerText.contains("紧张") || lowerText.contains("担心")) {
            emotionAnalysis.setEmotion("焦虑");
            emotionAnalysis.setScore("0.6");
        } else {
            emotionAnalysis.setEmotion("中性");
            emotionAnalysis.setScore("0.5");
        }

        emotionAnalysis.setReason("基于语音语气分析得出");
        return emotionAnalysis;
    }

    /**
     * 创建默认的情绪分析结果
     */
    private TagAnalysisDTO.EmotionAnalysis createDefaultEmotionAnalysis(String userInput) {

        // 语气情绪分析
        TagAnalysisDTO.EmotionAnalysis toneResult = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionToneAppId(),
                userInput,
                TagAnalysisDTO.EmotionAnalysis.class
        );

        TagAnalysisDTO.EmotionAnalysis toneEmotion = new TagAnalysisDTO.EmotionAnalysis();
        toneEmotion.setEmotion(toneResult.getEmotion());
        toneEmotion.setReason(toneResult.getReason());
        toneEmotion.setScore(toneResult.getScore());
        return toneEmotion;
    }


}